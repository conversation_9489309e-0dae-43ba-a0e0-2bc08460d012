<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="datePickerShortcuts"
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <!-- <el-select
                  v-model="queryParams.isTest"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" value="是" />
                  <el-option label="否" value="否" />
                </el-select> -->
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          border
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column label="名称" width="150" align="center">
            <template #default="scope">
              {{ onGetShowName(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column prop="ReportCount" label="筛查报告数量" align="center" />
          <el-table-column prop="CheckedCount" label="已查看人数" align="center" />
          <el-table-column prop="UnCheckedCount" label="未查看人数" align="center" />
          <el-table-column prop="CheckedRate" label="查询率" align="center">
            <template #default="scope">
              {{ calculateAmount([scope.row.CheckedRate, 100], "*").toFixed(2) + "%" }}
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import { CheckedReportInputDTO, CheckedReportItem } from "@/api/supplier-qingpai/types";
import Supplier_Qingpai_Api from "@/api/supplier-qingpai";
import { calculateAmount } from "@/utils";
const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "OptometryReportQuery",
});

const queryParams = ref<CheckedReportInputDTO>({
  BeginTime: dayjs().format("YYYY-MM-01 00:00:00"),
  EndTime: dayjs().format("YYYY-MM-DD 23:59:59"),
  SchoolName: null,
  SchoolCategory: null,
  Grade: null,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<CheckedReportItem>();

const handleQuery = () => {
  handleGetTableList();
};

const onGetShowName = (row: CheckedReportItem): string | undefined => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  if (copyData.Grade) {
    return row.Class;
  } else if (copyData.SchoolName) {
    return row.Grade;
  } else if (copyData.SchoolCategory) {
    return row.SchoolName;
  } else if (!copyData.SchoolCategory) {
    return row.SchoolCategory;
  } else {
    return undefined;
  }
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Supplier_Qingpai_Api.getCheckedReport(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data;
  }
  tableLoading.value = false;
};

const getSummaries 

watch(timeRange, (newVal) => {
  queryParams.value.BeginTime = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTime = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
